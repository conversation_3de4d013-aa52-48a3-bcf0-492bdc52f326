'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Star, ExternalLink, Search, Filter } from 'lucide-react';

interface Repository {
  id: string;
  githubId: number;
  name: string;
  fullName: string;
  description: string | null;
  language: string | null;
  starsCount: number;
  htmlUrl: string;
  updatedAt: string;
}

interface UserStar {
  id: string;
  starredAt: string;
  repository: Repository;
}

interface StarsListProps {
  initialData?: {
    data: UserStar[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

export function StarsList({ initialData }: StarsListProps) {
  const [stars, setStars] = useState<UserStar[]>(initialData?.data || []);
  const [pagination, setPagination] = useState(initialData?.pagination || {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [language, setLanguage] = useState('');
  const [sortBy, setSortBy] = useState('starredAt');
  const [sortOrder, setSortOrder] = useState('desc');

  const fetchStars = async (page = 1) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
      });

      if (search) params.append('search', search);
      if (language) params.append('language', language);

      const response = await fetch(`/api/stars?${params}`);
      if (!response.ok) throw new Error('Failed to fetch stars');

      const data = await response.json();
      setStars(data.data);
      setPagination(data.pagination);
    } catch (error) {
      console.error('Error fetching stars:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!initialData) {
      fetchStars();
    }
  }, [sortBy, sortOrder]);

  const handleSearch = () => {
    fetchStars(1);
  };

  const handlePageChange = (newPage: number) => {
    fetchStars(newPage);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN');
  };

  const getLanguageColor = (language: string | null) => {
    if (!language) return 'bg-gray-500';
    
    const colors: Record<string, string> = {
      JavaScript: 'bg-yellow-500',
      TypeScript: 'bg-blue-500',
      Python: 'bg-green-500',
      Java: 'bg-red-500',
      Go: 'bg-cyan-500',
      Rust: 'bg-orange-500',
      'C++': 'bg-purple-500',
      C: 'bg-gray-600',
      PHP: 'bg-indigo-500',
      Ruby: 'bg-red-600',
      Swift: 'bg-orange-600',
      Kotlin: 'bg-purple-600',
    };
    
    return colors[language] || 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      {/* 搜索和筛选 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="w-5 h-5" />
            搜索和筛选
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="搜索仓库名称或描述..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch} disabled={loading}>
              <Search className="w-4 h-4 mr-2" />
              搜索
            </Button>
          </div>
          
          <div className="flex gap-4">
            <Select value={language} onValueChange={setLanguage}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="选择编程语言" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">所有语言</SelectItem>
                <SelectItem value="JavaScript">JavaScript</SelectItem>
                <SelectItem value="TypeScript">TypeScript</SelectItem>
                <SelectItem value="Python">Python</SelectItem>
                <SelectItem value="Java">Java</SelectItem>
                <SelectItem value="Go">Go</SelectItem>
                <SelectItem value="Rust">Rust</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="starredAt">收藏时间</SelectItem>
                <SelectItem value="starsCount">Star 数量</SelectItem>
                <SelectItem value="updatedAt">更新时间</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortOrder} onValueChange={setSortOrder}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">降序</SelectItem>
                <SelectItem value="asc">升序</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="text-sm text-muted-foreground">
        共找到 {pagination.total} 个 starred repositories
      </div>

      {/* 仓库列表 */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : stars.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            没有找到 starred repositories
          </div>
        ) : (
          stars.map((star) => (
            <Card key={star.id}>
              <CardContent className="pt-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-semibold text-lg">
                        <a
                          href={star.repository.htmlUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="hover:text-blue-600 flex items-center gap-1"
                        >
                          {star.repository.fullName}
                          <ExternalLink className="w-4 h-4" />
                        </a>
                      </h3>
                    </div>
                    
                    {star.repository.description && (
                      <p className="text-muted-foreground mb-3">
                        {star.repository.description}
                      </p>
                    )}
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      {star.repository.language && (
                        <div className="flex items-center gap-1">
                          <div
                            className={`w-3 h-3 rounded-full ${getLanguageColor(star.repository.language)}`}
                          />
                          {star.repository.language}
                        </div>
                      )}
                      
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4" />
                        {star.repository.starsCount.toLocaleString()}
                      </div>
                      
                      <div>
                        收藏于 {formatDate(star.starredAt)}
                      </div>
                      
                      <div>
                        更新于 {formatDate(star.repository.updatedAt)}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* 分页 */}
      {pagination.totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            disabled={!pagination.hasPrev || loading}
            onClick={() => handlePageChange(pagination.page - 1)}
          >
            上一页
          </Button>
          
          <span className="flex items-center px-4">
            第 {pagination.page} 页，共 {pagination.totalPages} 页
          </span>
          
          <Button
            variant="outline"
            disabled={!pagination.hasNext || loading}
            onClick={() => handlePageChange(pagination.page + 1)}
          >
            下一页
          </Button>
        </div>
      )}
    </div>
  );
}
