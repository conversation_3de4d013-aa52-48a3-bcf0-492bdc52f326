'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  useSyncStatus, 
  formatSyncStatusMessage, 
  getSyncStatusColor,
  canStartSync,
  canCancelSync 
} from '@/hooks/use-sync-status';
import { Loader2, Download, X, CheckCircle, AlertCircle } from 'lucide-react';

export function SyncButton() {
  const { progress, isLoading, error, startSync, cancelSync, clearError } = useSyncStatus();

  const handleStartSync = async () => {
    if (error) clearError();
    await startSync();
  };

  const handleCancelSync = async () => {
    await cancelSync();
  };

  const getStatusIcon = () => {
    if (!progress) return <Download className="w-4 h-4" />;
    
    switch (progress.status) {
      case 'running':
        return <Loader2 className="w-4 h-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'error':
        return <AlertCircle className="w-4 h-4" />;
      case 'cancelled':
        return <X className="w-4 h-4" />;
      default:
        return <Download className="w-4 h-4" />;
    }
  };

  const getButtonText = () => {
    if (isLoading) return '处理中...';
    if (!progress || canStartSync(progress)) return '同步 GitHub Stars';
    if (canCancelSync(progress)) return '取消同步';
    return '同步完成';
  };

  const getButtonVariant = () => {
    if (!progress) return 'default';
    
    switch (progress.status) {
      case 'running':
        return 'destructive';
      case 'completed':
        return 'default';
      case 'error':
        return 'destructive';
      default:
        return 'default';
    }
  };

  return (
    <div className="space-y-4">
      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2 h-auto p-0 text-xs underline"
            >
              关闭
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* 同步按钮 */}
      <Button
        onClick={canCancelSync(progress) ? handleCancelSync : handleStartSync}
        disabled={isLoading || (progress?.status === 'completed')}
        variant={getButtonVariant()}
        className="w-full"
      >
        {getStatusIcon()}
        <span className="ml-2">{getButtonText()}</span>
      </Button>

      {/* 进度显示 */}
      {progress && (
        <div className="space-y-2">
          {/* 状态消息 */}
          <div className={`text-sm ${getSyncStatusColor(progress.status)}`}>
            {formatSyncStatusMessage(progress)}
          </div>

          {/* 进度条 */}
          {progress.status === 'running' && (
            <div className="space-y-1">
              <Progress value={progress.percentage} className="w-full" />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>
                  {progress.current} / {progress.total || '?'}
                </span>
                <span>{progress.percentage.toFixed(1)}%</span>
              </div>
            </div>
          )}

          {/* 完成信息 */}
          {progress.status === 'completed' && progress.completedAt && (
            <div className="text-xs text-muted-foreground">
              完成时间: {new Date(progress.completedAt).toLocaleString()}
            </div>
          )}

          {/* 错误信息 */}
          {progress.status === 'error' && progress.error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                {progress.error}
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}
    </div>
  );
}
