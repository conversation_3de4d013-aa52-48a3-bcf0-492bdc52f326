import * as React from "react";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

export interface TagChipProps extends React.HTMLAttributes<HTMLDivElement> {
  color?: string;
  onRemove?: () => void;
  removable?: boolean;
  size?: "sm" | "md" | "lg";
}

const sizeClasses = {
  sm: "text-xs px-2 py-0.5",
  md: "text-sm px-2.5 py-1",
  lg: "text-base px-3 py-1.5",
};

export function TagChip({
  children,
  className,
  color = "#6366F1",
  onRemove,
  removable = false,
  size = "md",
  ...props
}: TagChipProps) {
  const bgColor = `${color}20`; // 20% opacity version of the color

  return (
    <div
      className={cn(
        "inline-flex items-center gap-1 rounded-full font-medium transition-colors",
        sizeClasses[size],
        className
      )}
      style={{ backgroundColor: bgColor, color: color }}
      {...props}
    >
      {children}
      {removable && (
        <button
          type="button"
          onClick={onRemove}
          className="ml-1 rounded-full p-0.5 hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <X className={cn("h-3 w-3", size === "lg" && "h-4 w-4")} />
        </button>
      )}
    </div>
  );
}
