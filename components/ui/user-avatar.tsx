import * as React from "react";
import * as AvatarPrimitive from "@radix-ui/react-avatar";
import { cn } from "@/lib/utils";

interface UserAvatarProps extends React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root> {
  user: {
    name?: string | null;
    image?: string | null;
  };
  size?: "sm" | "md" | "lg";
}

const sizeClasses = {
  sm: "h-8 w-8",
  md: "h-10 w-10",
  lg: "h-12 w-12",
};

const UserAvatar = React.forwardRef<
  React.ElementRef<typeof AvatarPrimitive.Root>,
  UserAvatarProps
>(({ user, size = "md", className, ...props }, ref) => {
  const initials = user.name
    ? user.name
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
    : "?";

  return (
    <AvatarPrimitive.Root
      ref={ref}
      className={cn(
        "relative flex shrink-0 overflow-hidden rounded-full",
        sizeClasses[size],
        className
      )}
      {...props}
    >
      {user.image ? (
        <AvatarPrimitive.Image
          src={user.image}
          alt={user.name ?? "User avatar"}
          className="aspect-square h-full w-full"
        />
      ) : (
        <AvatarPrimitive.Fallback
          className="flex h-full w-full items-center justify-center rounded-full bg-muted"
        >
          <span className="text-sm font-medium">{initials}</span>
        </AvatarPrimitive.Fallback>
      )}
    </AvatarPrimitive.Root>
  );
});
UserAvatar.displayName = "UserAvatar";

export { UserAvatar };
