import * as React from "react";
import { <PERSON>, <PERSON><PERSON>For<PERSON>, Clock, Code } from "lucide-react";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "./card";
import { Button } from "./button";

export interface ProjectCardProps {
  name: string;
  fullName: string;
  description: string;
  language: string;
  starsCount: number;
  forksCount: number;
  updatedAt: string;
  htmlUrl: string;
  onStar?: () => void;
  isStarred?: boolean;
  tags?: Array<{ id: string; name: string; color: string }>;
}

export function ProjectCard({
  name,
  fullName,
  description,
  language,
  starsCount,
  forksCount,
  updatedAt,
  htmlUrl,
  onStar,
  isStarred,
  tags,
}: ProjectCardProps) {
  const formattedDate = new Date(updatedAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });

  return (
    <Card>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-xl hover:text-primary">
              <a href={htmlUrl} target="_blank" rel="noopener noreferrer">
                {name}
              </a>
            </CardTitle>
            <p className="text-sm text-muted-foreground">{fullName}</p>
          </div>
          {onStar && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onStar}
              className={isStarred ? "text-yellow-500" : ""}
            >
              <Star className="h-5 w-5" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <CardDescription className="line-clamp-2">{description}</CardDescription>
        {tags && tags.length > 0 && (
          <div className="mt-3 flex flex-wrap gap-2">
            {tags.map((tag) => (
              <span
                key={tag.id}
                className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium"
                style={{ backgroundColor: tag.color + "20", color: tag.color }}
              >
                {tag.name}
              </span>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex flex-wrap gap-4 text-sm text-muted-foreground">
        {language && (
          <div className="flex items-center gap-1">
            <Code className="h-4 w-4" />
            {language}
          </div>
        )}
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4" />
          {starsCount.toLocaleString()}
        </div>
        <div className="flex items-center gap-1">
          <GitFork className="h-4 w-4" />
          {forksCount.toLocaleString()}
        </div>
        <div className="flex items-center gap-1">
          <Clock className="h-4 w-4" />
          Updated {formattedDate}
        </div>
      </CardFooter>
    </Card>
  );
}
