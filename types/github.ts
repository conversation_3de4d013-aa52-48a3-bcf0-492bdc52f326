// GitHub API 响应类型定义

export interface GitHubUser {
  id: number;
  login: string;
  avatar_url: string;
  name: string | null;
  email: string | null;
  public_repos: number;
  public_gists: number;
  followers: number;
  following: number;
  created_at: string;
  updated_at: string;
}

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
  html_url: string;
  clone_url: string;
  git_url: string;
  ssh_url: string;
  language: string | null;
  stargazers_count: number;
  watchers_count: number;
  forks_count: number;
  size: number;
  default_branch: string;
  open_issues_count: number;
  topics: string[];
  has_issues: boolean;
  has_projects: boolean;
  has_wiki: boolean;
  has_pages: boolean;
  has_downloads: boolean;
  archived: boolean;
  disabled: boolean;
  visibility: 'public' | 'private';
  pushed_at: string | null;
  created_at: string;
  updated_at: string;
  owner: {
    id: number;
    login: string;
    avatar_url: string;
    type: 'User' | 'Organization';
  };
  license: {
    key: string;
    name: string;
    spdx_id: string;
    url: string | null;
  } | null;
}

export interface GitHubStarredRepository extends GitHubRepository {
  starred_at: string;
}

export interface GitHubReadme {
  name: string;
  path: string;
  sha: string;
  size: number;
  url: string;
  html_url: string;
  git_url: string;
  download_url: string;
  type: 'file';
  content: string;
  encoding: 'base64';
}

export interface GitHubRateLimit {
  limit: number;
  remaining: number;
  reset: number;
  used: number;
  resource: string;
}

export interface GitHubRateLimitResponse {
  resources: {
    core: GitHubRateLimit;
    search: GitHubRateLimit;
    graphql: GitHubRateLimit;
    integration_manifest: GitHubRateLimit;
    source_import: GitHubRateLimit;
    code_scanning_upload: GitHubRateLimit;
    actions_runner_registration: GitHubRateLimit;
    scim: GitHubRateLimit;
  };
  rate: GitHubRateLimit;
}

// API 错误响应
export interface GitHubApiError {
  message: string;
  documentation_url?: string;
  errors?: Array<{
    resource: string;
    field: string;
    code: string;
  }>;
}

// 分页信息
export interface PaginationInfo {
  page: number;
  per_page: number;
  total_count?: number;
  has_next: boolean;
  has_prev: boolean;
}

// 同步状态类型
export type SyncStatus = 'idle' | 'running' | 'completed' | 'error' | 'cancelled';

export interface SyncProgress {
  status: SyncStatus;
  current: number;
  total: number;
  percentage: number;
  message: string;
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
}

// API 请求选项
export interface GitHubApiOptions {
  page?: number;
  per_page?: number;
  sort?: 'created' | 'updated' | 'pushed' | 'full_name';
  direction?: 'asc' | 'desc';
}

export interface StarredReposOptions extends GitHubApiOptions {
  sort?: 'created' | 'updated';
}

// 数据库映射类型
export interface RepositoryData {
  githubId: number;
  name: string;
  fullName: string;
  description: string | null;
  language: string | null;
  starsCount: number;
  htmlUrl: string;
  updatedAt: Date;
}

export interface UserStarData {
  userId: string;
  repositoryId: string;
  starredAt: Date;
}

// API 响应包装类型
export interface ApiResponse<T> {
  data: T;
  pagination?: PaginationInfo;
  rateLimit?: GitHubRateLimit;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
}

// 同步配置
export interface SyncConfig {
  batchSize: number;
  delayBetweenRequests: number;
  maxRetries: number;
  retryDelay: number;
}

// 重试配置
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
}

// GitHub API 客户端配置
export interface GitHubApiConfig {
  accessToken: string;
  baseUrl?: string;
  userAgent?: string;
  timeout?: number;
  retryConfig?: RetryConfig;
}
