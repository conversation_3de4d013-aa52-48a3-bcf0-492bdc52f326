'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { SyncProgress, SyncStatus } from '@/types/github';

interface UseSyncStatusReturn {
  progress: SyncProgress | null;
  isLoading: boolean;
  error: string | null;
  startSync: () => Promise<void>;
  cancelSync: () => Promise<void>;
  clearError: () => void;
}

export function useSyncStatus(): UseSyncStatusReturn {
  const [progress, setProgress] = useState<SyncProgress | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 获取同步状态
  const fetchSyncStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/sync/status');
      if (!response.ok) {
        throw new Error('Failed to fetch sync status');
      }
      const data = await response.json();
      setProgress(data);
      
      // 如果同步完成或出错，停止轮询
      if (data.status === 'completed' || data.status === 'error' || data.status === 'cancelled') {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      }
    } catch (err) {
      console.error('Error fetching sync status:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  }, []);

  // 开始轮询同步状态
  const startPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    // 立即获取一次状态
    fetchSyncStatus();
    
    // 每2秒轮询一次
    intervalRef.current = setInterval(fetchSyncStatus, 2000);
  }, [fetchSyncStatus]);

  // 停止轮询
  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // 开始同步
  const startSync = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/sync/stars', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start sync');
      }

      const data = await response.json();
      setProgress(data.progress);
      
      // 开始轮询状态
      startPolling();
      
    } catch (err) {
      console.error('Error starting sync:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [startPolling]);

  // 取消同步
  const cancelSync = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/sync/cancel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to cancel sync');
      }

      const data = await response.json();
      setProgress(data.progress);
      
      // 停止轮询
      stopPolling();
      
    } catch (err) {
      console.error('Error cancelling sync:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [stopPolling]);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 组件挂载时获取当前状态
  useEffect(() => {
    fetchSyncStatus();
    
    // 如果有正在运行的同步，开始轮询
    return () => {
      stopPolling();
    };
  }, [fetchSyncStatus, stopPolling]);

  // 监听 progress 变化，如果是运行状态且没有轮询，则开始轮询
  useEffect(() => {
    if (progress?.status === 'running' && !intervalRef.current) {
      startPolling();
    }
  }, [progress?.status, startPolling]);

  // 清理定时器
  useEffect(() => {
    return () => {
      stopPolling();
    };
  }, [stopPolling]);

  return {
    progress,
    isLoading,
    error,
    startSync,
    cancelSync,
    clearError,
  };
}

// 格式化同步状态消息
export function formatSyncStatusMessage(progress: SyncProgress): string {
  switch (progress.status) {
    case 'idle':
      return '准备就绪';
    case 'running':
      return progress.message || '正在同步...';
    case 'completed':
      return progress.message || '同步完成';
    case 'error':
      return progress.error || '同步失败';
    case 'cancelled':
      return '同步已取消';
    default:
      return '未知状态';
  }
}

// 获取同步状态的颜色
export function getSyncStatusColor(status: SyncStatus): string {
  switch (status) {
    case 'idle':
      return 'text-gray-500';
    case 'running':
      return 'text-blue-500';
    case 'completed':
      return 'text-green-500';
    case 'error':
      return 'text-red-500';
    case 'cancelled':
      return 'text-yellow-500';
    default:
      return 'text-gray-500';
  }
}

// 判断是否可以开始新的同步
export function canStartSync(progress: SyncProgress | null): boolean {
  return !progress || progress.status === 'completed' || progress.status === 'error' || progress.status === 'cancelled';
}

// 判断是否可以取消同步
export function canCancelSync(progress: SyncProgress | null): boolean {
  return progress?.status === 'running';
}
