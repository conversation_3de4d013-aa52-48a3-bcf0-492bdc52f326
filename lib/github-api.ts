import {
  GitHubUser,
  GitHubRepository,
  GitHubStarredRepository,
  GitHubReadme,
  GitHubRateLimitResponse,
  GitHubApiConfig,
  StarredReposOptions,
  ApiResponse,
  PaginationInfo,
  RetryConfig,
} from '@/types/github';
import { withRetry, parseGitHubError, logError } from './error-handler';

// 默认配置
const DEFAULT_CONFIG: Partial<GitHubApiConfig> = {
  baseUrl: 'https://api.github.com',
  userAgent: 'gh-star-app/1.0',
  timeout: 30000,
  retryConfig: {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
  },
};

export class GitHubApiClient {
  private config: GitHubApiConfig;

  constructor(config: GitHubApiConfig) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  // 创建请求头
  private getHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.config.accessToken}`,
      'Accept': 'application/vnd.github+json',
      'User-Agent': this.config.userAgent!,
      'X-GitHub-Api-Version': '2022-11-28',
    };
  }

  // 发送 HTTP 请求
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const url = `${this.config.baseUrl}${endpoint}`;
    
    const requestOptions: RequestInit = {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
      signal: AbortSignal.timeout(this.config.timeout!),
    };

    return withRetry(
      async () => {
        const response = await fetch(url, requestOptions);
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
          (error as any).response = {
            status: response.status,
            statusText: response.statusText,
            headers: Object.fromEntries(response.headers.entries()),
            data: errorData,
          };
          throw error;
        }

        return response;
      },
      this.config.retryConfig!,
      (error, attempt) => {
        logError(error, `Request to ${endpoint}, attempt ${attempt}`);
      }
    );
  }

  // 解析分页信息
  private parsePaginationInfo(response: Response): PaginationInfo {
    const linkHeader = response.headers.get('link');
    const totalCountHeader = response.headers.get('x-total-count');
    
    let hasNext = false;
    let hasPrev = false;
    
    if (linkHeader) {
      hasNext = linkHeader.includes('rel="next"');
      hasPrev = linkHeader.includes('rel="prev"');
    }

    // 从 URL 参数中提取当前页码和每页数量
    const url = new URL(response.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const perPage = parseInt(url.searchParams.get('per_page') || '30');

    return {
      page,
      per_page: perPage,
      total_count: totalCountHeader ? parseInt(totalCountHeader) : undefined,
      has_next: hasNext,
      has_prev: hasPrev,
    };
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<GitHubUser> {
    const response = await this.request<GitHubUser>('/user');
    return response.json();
  }

  // 获取 API 使用限制
  async getRateLimit(): Promise<GitHubRateLimitResponse> {
    const response = await this.request<GitHubRateLimitResponse>('/rate_limit');
    return response.json();
  }

  // 获取用户的 starred repositories（分页）
  async getStarredRepos(
    options: StarredReposOptions = {}
  ): Promise<ApiResponse<GitHubStarredRepository[]>> {
    const params = new URLSearchParams();
    
    if (options.page) params.append('page', options.page.toString());
    if (options.per_page) params.append('per_page', options.per_page.toString());
    if (options.sort) params.append('sort', options.sort);
    if (options.direction) params.append('direction', options.direction);

    const endpoint = `/user/starred?${params.toString()}`;
    const response = await this.request<GitHubStarredRepository[]>(endpoint, {
      headers: {
        'Accept': 'application/vnd.github.star+json',
      },
    });

    const data = await response.json();
    const pagination = this.parsePaginationInfo(response);
    
    // 获取 rate limit 信息
    const rateLimitRemaining = response.headers.get('x-ratelimit-remaining');
    const rateLimitReset = response.headers.get('x-ratelimit-reset');
    
    const rateLimit = rateLimitRemaining ? {
      limit: parseInt(response.headers.get('x-ratelimit-limit') || '0'),
      remaining: parseInt(rateLimitRemaining),
      reset: parseInt(rateLimitReset || '0'),
      used: 0,
      resource: 'core',
    } : undefined;

    return {
      data,
      pagination,
      rateLimit,
    };
  }

  // 获取所有 starred repositories（自动分页）
  async getAllStarredRepos(
    onProgress?: (current: number, total?: number) => void
  ): Promise<GitHubStarredRepository[]> {
    const allRepos: GitHubStarredRepository[] = [];
    let page = 1;
    let hasMore = true;
    const perPage = 100; // GitHub API 最大值

    while (hasMore) {
      const response = await this.getStarredRepos({
        page,
        per_page: perPage,
        sort: 'created',
        direction: 'desc',
      });

      allRepos.push(...response.data);
      
      if (onProgress) {
        onProgress(allRepos.length, response.pagination?.total_count);
      }

      hasMore = response.pagination?.has_next || false;
      page++;

      // 添加延迟以避免触发 rate limit
      if (hasMore) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return allRepos;
  }

  // 获取单个仓库信息
  async getRepository(owner: string, repo: string): Promise<GitHubRepository> {
    const response = await this.request<GitHubRepository>(`/repos/${owner}/${repo}`);
    return response.json();
  }

  // 获取仓库 README
  async getRepositoryReadme(owner: string, repo: string): Promise<GitHubReadme | null> {
    try {
      const response = await this.request<GitHubReadme>(`/repos/${owner}/${repo}/readme`);
      return response.json();
    } catch (error) {
      const githubError = parseGitHubError(error);
      if (githubError.statusCode === 404) {
        return null; // README 不存在
      }
      throw githubError;
    }
  }

  // 解码 README 内容
  decodeReadmeContent(readme: GitHubReadme): string {
    if (readme.encoding === 'base64') {
      return Buffer.from(readme.content, 'base64').toString('utf-8');
    }
    return readme.content;
  }

  // 检查 API 使用限制
  async checkRateLimit(): Promise<{
    canProceed: boolean;
    remaining: number;
    resetTime: Date;
    waitTime?: number;
  }> {
    const rateLimit = await this.getRateLimit();
    const coreLimit = rateLimit.resources.core;
    
    const resetTime = new Date(coreLimit.reset * 1000);
    const now = new Date();
    const waitTime = resetTime.getTime() - now.getTime();

    return {
      canProceed: coreLimit.remaining > 0,
      remaining: coreLimit.remaining,
      resetTime,
      waitTime: waitTime > 0 ? waitTime : undefined,
    };
  }

  // 等待 rate limit 重置
  async waitForRateLimit(): Promise<void> {
    const { canProceed, waitTime } = await this.checkRateLimit();
    
    if (!canProceed && waitTime) {
      console.log(`Rate limit exceeded. Waiting ${Math.ceil(waitTime / 1000)} seconds...`);
      await new Promise(resolve => setTimeout(resolve, waitTime + 1000)); // 额外等待1秒
    }
  }
}

// 创建 GitHub API 客户端实例
export function createGitHubApiClient(accessToken: string): GitHubApiClient {
  return new GitHubApiClient({ accessToken });
}
