import { prisma } from './prisma'

// User operations
export async function findOrCreateUser(githubUserData: {
  githubId: number
  username: string
  avatarUrl: string
  email?: string
  accessToken: string
}) {
  return prisma.user.upsert({
    where: { githubId: githubUserData.githubId },
    update: {
      username: githubUserData.username,
      avatarUrl: githubUserData.avatarUrl,
      email: githubUserData.email,
      accessToken: githubUserData.accessToken,
    },
    create: githubUserData,
  })
}

// Repository operations
export async function findOrCreateRepository(repoData: {
  githubId: number
  name: string
  fullName: string
  description?: string
  language?: string
  starsCount: number
  htmlUrl: string
  updatedAt: Date
}) {
  return prisma.repository.upsert({
    where: { githubId: repoData.githubId },
    update: {
      name: repoData.name,
      fullName: repoData.fullName,
      description: repoData.description,
      language: repoData.language,
      starsCount: repoData.starsCount,
      htmlUrl: repoData.htmlUrl,
      updatedAt: repoData.updatedAt,
    },
    create: repoData,
  })
}

// Star operations
export async function createUserStar(data: {
  userId: string
  repositoryId: string
  starredAt: Date
}) {
  return prisma.userStar.create({
    data,
  })
}

export async function removeUserStar(userId: string, repositoryId: string) {
  return prisma.userStar.delete({
    where: {
      userId_repositoryId: {
        userId,
        repositoryId,
      },
    },
  })
}

// Tag operations
export async function createTag(data: {
  name: string
  color: string
  createdByUserId: string
}) {
  return prisma.tag.create({
    data,
  })
}

export async function addTagToRepository(repositoryId: string, tagId: string) {
  return prisma.repositoryTag.create({
    data: {
      repositoryId,
      tagId,
    },
  })
}

// AI Summary operations
export async function upsertAiSummary(data: {
  repositoryId: string
  summaryZh: string
  summaryEn: string
}) {
  return prisma.aiSummary.upsert({
    where: { repositoryId: data.repositoryId },
    update: {
      summaryZh: data.summaryZh,
      summaryEn: data.summaryEn,
      processedAt: new Date(),
    },
    create: {
      ...data,
      processedAt: new Date(),
    },
  })
}

// Query operations
export async function getUserStarredRepos(
  userId: string,
  options: {
    skip?: number
    take?: number
    orderBy?: {
      field: 'starredAt' | 'starsCount' | 'updatedAt'
      order: 'asc' | 'desc'
    }
    where?: {
      language?: string
      tagIds?: string[]
      searchQuery?: string
    }
  }
) {
  const { skip = 0, take = 20, orderBy, where } = options

  const whereClause = {
    userId,
    ...(where?.language && {
      repository: {
        language: where.language,
      },
    }),
    ...(where?.tagIds?.length && {
      repository: {
        tags: {
          some: {
            tagId: {
              in: where.tagIds,
            },
          },
        },
      },
    }),
    ...(where?.searchQuery && {
      repository: {
        OR: [
          { name: { contains: where.searchQuery, mode: 'insensitive' } },
          { description: { contains: where.searchQuery, mode: 'insensitive' } },
        ],
      },
    }),
  }

  return prisma.userStar.findMany({
    where: whereClause,
    skip,
    take,
    orderBy: orderBy
      ? {
          [orderBy.field]: orderBy.order,
        }
      : undefined,
    include: {
      repository: {
        include: {
          tags: {
            include: {
              tag: true,
            },
          },
          aiSummary: true,
        },
      },
    },
  })
}
