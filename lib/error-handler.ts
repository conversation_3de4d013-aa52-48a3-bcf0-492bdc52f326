import { ApiError, RetryConfig } from '@/types/github';

// 错误类型枚举
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  SERVER_ERROR = 'SERVER_ERROR',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN = 'UNKNOWN',
}

// 自定义错误类
export class GitHubApiError extends Error {
  public readonly type: ErrorType;
  public readonly statusCode?: number;
  public readonly retryAfter?: number;
  public readonly details?: any;

  constructor(
    message: string,
    type: ErrorType,
    statusCode?: number,
    retryAfter?: number,
    details?: any
  ) {
    super(message);
    this.name = 'GitHubApiError';
    this.type = type;
    this.statusCode = statusCode;
    this.retryAfter = retryAfter;
    this.details = details;
  }
}

// 解析 GitHub API 错误
export function parseGitHubError(error: any): GitHubApiError {
  if (error instanceof GitHubApiError) {
    return error;
  }

  // 网络错误
  if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
    return new GitHubApiError(
      '网络连接失败，请检查网络连接',
      ErrorType.NETWORK_ERROR,
      undefined,
      undefined,
      error
    );
  }

  // 超时错误
  if (error.code === 'ETIMEDOUT' || error.message?.includes('timeout')) {
    return new GitHubApiError(
      '请求超时，请稍后重试',
      ErrorType.TIMEOUT,
      undefined,
      undefined,
      error
    );
  }

  // HTTP 错误
  if (error.response) {
    const { status, data } = error.response;
    const retryAfter = error.response.headers['retry-after']
      ? parseInt(error.response.headers['retry-after'])
      : undefined;

    switch (status) {
      case 401:
        return new GitHubApiError(
          'GitHub 访问令牌无效或已过期，请重新登录',
          ErrorType.UNAUTHORIZED,
          status,
          undefined,
          data
        );

      case 403:
        if (data?.message?.includes('rate limit')) {
          return new GitHubApiError(
            'GitHub API 调用频率限制，请稍后重试',
            ErrorType.RATE_LIMIT_EXCEEDED,
            status,
            retryAfter,
            data
          );
        }
        return new GitHubApiError(
          'GitHub API 访问被拒绝，权限不足',
          ErrorType.FORBIDDEN,
          status,
          undefined,
          data
        );

      case 404:
        return new GitHubApiError(
          '请求的资源不存在',
          ErrorType.NOT_FOUND,
          status,
          undefined,
          data
        );

      case 422:
        return new GitHubApiError(
          '请求参数无效',
          ErrorType.UNKNOWN,
          status,
          undefined,
          data
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return new GitHubApiError(
          'GitHub 服务器错误，请稍后重试',
          ErrorType.SERVER_ERROR,
          status,
          retryAfter,
          data
        );

      default:
        return new GitHubApiError(
          `GitHub API 错误: ${data?.message || '未知错误'}`,
          ErrorType.UNKNOWN,
          status,
          undefined,
          data
        );
    }
  }

  // 其他错误
  return new GitHubApiError(
    error.message || '未知错误',
    ErrorType.UNKNOWN,
    undefined,
    undefined,
    error
  );
}

// 判断错误是否可重试
export function isRetryableError(error: GitHubApiError): boolean {
  switch (error.type) {
    case ErrorType.NETWORK_ERROR:
    case ErrorType.TIMEOUT:
    case ErrorType.SERVER_ERROR:
    case ErrorType.RATE_LIMIT_EXCEEDED:
      return true;
    default:
      return false;
  }
}

// 计算重试延迟（指数退避）
export function calculateRetryDelay(
  attempt: number,
  config: RetryConfig,
  retryAfter?: number
): number {
  if (retryAfter) {
    return retryAfter * 1000; // 转换为毫秒
  }

  const delay = Math.min(
    config.baseDelay * Math.pow(config.backoffFactor, attempt),
    config.maxDelay
  );

  // 添加随机抖动，避免雷群效应
  const jitter = delay * 0.1 * Math.random();
  return delay + jitter;
}

// 重试装饰器
export async function withRetry<T>(
  operation: () => Promise<T>,
  config: RetryConfig,
  onRetry?: (error: GitHubApiError, attempt: number) => void
): Promise<T> {
  let lastError: GitHubApiError;

  for (let attempt = 0; attempt <= config.maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = parseGitHubError(error);

      // 如果不是可重试的错误，直接抛出
      if (!isRetryableError(lastError)) {
        throw lastError;
      }

      // 如果已经是最后一次尝试，抛出错误
      if (attempt === config.maxRetries) {
        throw lastError;
      }

      // 计算延迟时间
      const delay = calculateRetryDelay(attempt, config, lastError.retryAfter);

      // 调用重试回调
      if (onRetry) {
        onRetry(lastError, attempt + 1);
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

// 创建 API 错误响应
export function createApiError(
  code: string,
  message: string,
  details?: any
): ApiError {
  return {
    code,
    message,
    details,
  };
}

// 错误日志记录
export function logError(error: GitHubApiError, context?: string): void {
  const logData = {
    timestamp: new Date().toISOString(),
    context,
    type: error.type,
    message: error.message,
    statusCode: error.statusCode,
    retryAfter: error.retryAfter,
    details: error.details,
  };

  console.error('GitHub API Error:', logData);
}

// 用户友好的错误消息
export function getUserFriendlyMessage(error: GitHubApiError): string {
  switch (error.type) {
    case ErrorType.NETWORK_ERROR:
      return '网络连接失败，请检查网络连接后重试';
    case ErrorType.RATE_LIMIT_EXCEEDED:
      return 'GitHub API 调用次数已达上限，请稍后重试';
    case ErrorType.UNAUTHORIZED:
      return '登录已过期，请重新登录';
    case ErrorType.FORBIDDEN:
      return '权限不足，无法访问该资源';
    case ErrorType.NOT_FOUND:
      return '请求的资源不存在';
    case ErrorType.SERVER_ERROR:
      return 'GitHub 服务暂时不可用，请稍后重试';
    case ErrorType.TIMEOUT:
      return '请求超时，请稍后重试';
    default:
      return error.message || '发生未知错误，请稍后重试';
  }
}
