import { auth } from '@/auth';
import GitHubButton from '@/components/auth/github-button';
import { redirect } from 'next/navigation';

export const runtime = 'nodejs';

export default async function Home() {
  const session = await auth();
  
  // Redirect to dashboard if already logged in
  if (session) {
    redirect('/dashboard');
  }

  return (
    <div className="min-h-screen flex flex-col justify-center items-center px-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-2">GitHub Star Manager</h1>
          <p className="text-gray-600 mb-8">
            Organize and manage your GitHub starred repositories effortlessly.
          </p>
          <GitHubButton isSignIn={true} />
        </div>
        
        <div className="mt-8 text-center text-sm text-gray-600">
          <p>Sign in with GitHub to:</p>
          <ul className="mt-4 space-y-2">
            <li>• View and organize your starred repositories</li>
            <li>• Add custom tags and notes</li>
            <li>• Get AI-powered repository summaries</li>
            <li>• Export your data in various formats</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
