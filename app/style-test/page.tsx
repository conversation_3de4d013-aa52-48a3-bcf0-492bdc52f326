export default function StyleTestPage() {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">样式测试页面</h1>
        
        {/* 基础样式测试 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">基础样式测试</h2>
          <div className="space-y-4">
            <p className="text-gray-600">这是一段普通文本，用于测试基础样式。</p>
            <div className="flex space-x-4">
              <button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                蓝色按钮
              </button>
              <button className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                绿色按钮
              </button>
              <button className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
                红色按钮
              </button>
            </div>
          </div>
        </div>

        {/* 网格布局测试 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">网格布局测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="bg-blue-100 p-4 rounded">卡片 1</div>
            <div className="bg-green-100 p-4 rounded">卡片 2</div>
            <div className="bg-yellow-100 p-4 rounded">卡片 3</div>
            <div className="bg-purple-100 p-4 rounded">卡片 4</div>
            <div className="bg-pink-100 p-4 rounded">卡片 5</div>
            <div className="bg-indigo-100 p-4 rounded">卡片 6</div>
          </div>
        </div>

        {/* 表单样式测试 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">表单样式测试</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                输入框
              </label>
              <input 
                type="text" 
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入内容"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                选择框
              </label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option>选项 1</option>
                <option>选项 2</option>
                <option>选项 3</option>
              </select>
            </div>
          </div>
        </div>

        {/* 响应式测试 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">响应式测试</h2>
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded">
            <p className="text-sm md:text-base lg:text-lg">
              这个文本在不同屏幕尺寸下会有不同的大小
            </p>
          </div>
        </div>

        {/* 动画测试 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">动画测试</h2>
          <div className="space-y-4">
            <div className="w-16 h-16 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-16 h-16 bg-green-500 rounded-full animate-pulse"></div>
            <div className="w-16 h-16 bg-red-500 rounded-full animate-spin"></div>
          </div>
        </div>

        {/* 状态指示 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">状态指示</h2>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-green-700">成功状态</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="text-yellow-700">警告状态</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-red-700">错误状态</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
