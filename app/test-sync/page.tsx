import { auth } from '@/auth';
import { redirect } from 'next/navigation';
import { SyncButton } from '@/components/sync/sync-button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default async function TestSyncPage() {
  const session = await auth();

  if (!session?.user) {
    redirect('/');
  }

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">GitHub Stars 同步测试</h1>
          <p className="text-muted-foreground mt-2">
            测试 GitHub API 集成功能，同步你的 starred repositories
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>用户信息</CardTitle>
            <CardDescription>当前登录的 GitHub 用户信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center space-x-4">
              {session.user.image && (
                <img
                  src={session.user.image}
                  alt={session.user.name || 'User'}
                  className="w-12 h-12 rounded-full"
                />
              )}
              <div>
                <p className="font-medium">{session.user.name}</p>
                <p className="text-sm text-muted-foreground">{session.user.email}</p>
                {(session.user as any).username && (
                  <p className="text-sm text-muted-foreground">
                    @{(session.user as any).username}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>同步 GitHub Stars</CardTitle>
            <CardDescription>
              点击下方按钮开始同步你的 GitHub starred repositories 到本地数据库
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SyncButton />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>功能说明</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">已实现的功能：</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>GitHub OAuth 认证（已完成）</li>
                <li>GitHub API 客户端封装</li>
                <li>获取用户 starred repositories</li>
                <li>分页处理和批量同步</li>
                <li>API Rate Limit 检查和处理</li>
                <li>错误处理和重试机制</li>
                <li>同步进度实时显示</li>
                <li>数据库存储和映射</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">API 端点：</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li><code>POST /api/sync/stars</code> - 开始同步</li>
                <li><code>GET /api/sync/status</code> - 获取同步状态</li>
                <li><code>POST /api/sync/cancel</code> - 取消同步</li>
                <li><code>GET /api/stars</code> - 获取已同步的 stars</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">技术特性：</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>支持处理大量 starred repositories（1000+）</li>
                <li>自动分页获取所有数据</li>
                <li>智能重试和指数退避</li>
                <li>Rate Limit 自动处理</li>
                <li>实时进度更新</li>
                <li>可中断的同步过程</li>
                <li>增量同步（避免重复数据）</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
