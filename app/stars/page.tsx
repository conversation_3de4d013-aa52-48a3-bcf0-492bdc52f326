import { auth } from '@/auth';
import { redirect } from 'next/navigation';
import { getUserStarredRepos } from '@/lib/db';
import { StarsList } from '@/components/stars/stars-list';

export default async function StarsPage() {
  const session = await auth();

  if (!session?.user?.id) {
    redirect('/');
  }

  // 获取初始数据
  const result = await getUserStarredRepos(session.user.id, {
    take: 20,
    orderBy: {
      field: 'starredAt',
      order: 'desc',
    },
  });

  const initialData = {
    data: result.stars,
    pagination: {
      page: 1,
      limit: 20,
      total: result.total,
      totalPages: Math.ceil(result.total / 20),
      hasNext: result.total > 20,
      hasPrev: false,
    },
  };

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">我的 GitHub Stars</h1>
          <p className="text-muted-foreground mt-2">
            管理和浏览你收藏的 GitHub repositories
          </p>
        </div>

        <StarsList initialData={initialData} />
      </div>
    </div>
  );
}
