import { auth } from '@/auth';
import UserProfile from '@/components/auth/user-profile';
import GitHubButton from '@/components/auth/github-button';
import { redirect } from 'next/navigation';

export const runtime = 'nodejs';

export default async function Dashboard() {
  const session = await auth();

  if (!session) {
    redirect('/');
  }

  return (
    <div className="min-h-screen flex flex-col">
      <nav className="bg-white shadow-sm dark:bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-xl font-semibold dark:text-white">GitHub Star Manager</h1>
            
            <div className="flex items-center gap-4">
              <UserProfile />
              <GitHubButton isSignIn={false} />
            </div>
          </div>
        </div>
      </nav>

      <main className="flex-1 max-w-7xl w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-medium dark:text-white">Your Starred Repositories</h2>
            <div className="flex gap-2">
              <button className="px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600">
                Sync Stars
              </button>
              <button className="px-3 py-1.5 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                Add Tag
              </button>
            </div>
          </div>
          
          {/* Placeholder for repository list */}
          <div className="space-y-4">
            <div className="p-4 text-center text-gray-500 dark:text-gray-400 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
              <p className="mb-2">No repositories loaded yet</p>
              <p className="text-sm">Click &ldquo;Sync Stars&rdquo; to fetch your starred repositories from GitHub</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}