import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';

export const runtime = 'nodejs';
import { getUserStarredRepos } from '@/lib/db';

// 获取用户的 starred repositories
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);

    // 解析查询参数
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const language = searchParams.get('language') || undefined;
    const search = searchParams.get('search') || undefined;
    const sortBy = searchParams.get('sortBy') || 'starredAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const tagIds = searchParams.get('tagIds')?.split(',').filter(Boolean) || undefined;

    // 验证参数
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    const validSortFields = ['starredAt', 'starsCount', 'updatedAt'];
    if (!validSortFields.includes(sortBy)) {
      return NextResponse.json(
        { error: 'Invalid sort field' },
        { status: 400 }
      );
    }

    const validSortOrders = ['asc', 'desc'];
    if (!validSortOrders.includes(sortOrder)) {
      return NextResponse.json(
        { error: 'Invalid sort order' },
        { status: 400 }
      );
    }

    // 计算分页参数
    const skip = (page - 1) * limit;

    // 查询数据
    const result = await getUserStarredRepos(userId, {
      skip,
      take: limit,
      orderBy: {
        field: sortBy as 'starredAt' | 'starsCount' | 'updatedAt',
        order: sortOrder as 'asc' | 'desc',
      },
      where: {
        language,
        tagIds,
        searchQuery: search,
      },
    });

    // 计算总页数
    const totalPages = Math.ceil(result.total / limit);

    return NextResponse.json({
      data: result.stars,
      pagination: {
        page,
        limit,
        total: result.total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      filters: {
        language,
        search,
        tagIds,
      },
      sort: {
        field: sortBy,
        order: sortOrder,
      },
    });

  } catch (error) {
    console.error('Get stars error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
