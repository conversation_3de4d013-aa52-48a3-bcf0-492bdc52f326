import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';

export const runtime = 'nodejs';
import { createGitHubApiClient } from '@/lib/github-api';
import { findOrCreateRepository, createUserStar } from '@/lib/db';
import { prisma } from '@/lib/prisma';
import { parseGitHubError, getUserFriendlyMessage } from '@/lib/error-handler';
import { SyncProgress, SyncStatus } from '@/types/github';

// 内存中的同步状态存储（生产环境应使用 Redis）
const syncStates = new Map<string, SyncProgress>();

// 同步用户的 starred repositories
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    
    // 检查是否已有同步任务在运行
    const currentSync = syncStates.get(userId);
    if (currentSync && currentSync.status === 'running') {
      return NextResponse.json(
        { error: 'Sync already in progress' },
        { status: 409 }
      );
    }

    // 获取用户的 access token
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { accessToken: true },
    });

    if (!user?.accessToken) {
      return NextResponse.json(
        { error: 'GitHub access token not found' },
        { status: 400 }
      );
    }

    // 初始化同步状态
    const syncProgress: SyncProgress = {
      status: 'running',
      current: 0,
      total: 0,
      percentage: 0,
      message: '正在初始化同步...',
      startedAt: new Date(),
    };
    syncStates.set(userId, syncProgress);

    // 异步执行同步任务
    performSync(userId, user.accessToken).catch(error => {
      console.error('Sync error:', error);
      const errorMessage = parseGitHubError(error);
      syncStates.set(userId, {
        ...syncProgress,
        status: 'error',
        message: getUserFriendlyMessage(errorMessage),
        error: errorMessage.message,
        completedAt: new Date(),
      });
    });

    return NextResponse.json({
      message: 'Sync started successfully',
      progress: syncProgress,
    });

  } catch (error) {
    console.error('Sync initiation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 执行同步任务
async function performSync(userId: string, accessToken: string) {
  const client = createGitHubApiClient(accessToken);
  
  try {
    // 更新状态：检查 API 限制
    updateSyncProgress(userId, {
      message: '检查 GitHub API 使用限制...',
    });

    const rateLimitCheck = await client.checkRateLimit();
    if (!rateLimitCheck.canProceed) {
      throw new Error(`GitHub API 使用限制已达上限，请在 ${rateLimitCheck.resetTime.toLocaleString()} 后重试`);
    }

    // 更新状态：获取 starred repositories
    updateSyncProgress(userId, {
      message: '正在获取 starred repositories...',
    });

    const starredRepos = await client.getAllStarredRepos((current, total) => {
      updateSyncProgress(userId, {
        current,
        total: total || 0,
        percentage: total ? Math.round((current / total) * 50) : 0, // 获取数据占50%
        message: `正在获取 starred repositories... (${current}${total ? `/${total}` : ''})`,
      });
    });

    if (starredRepos.length === 0) {
      updateSyncProgress(userId, {
        status: 'completed',
        message: '没有找到 starred repositories',
        completedAt: new Date(),
      });
      return;
    }

    // 更新状态：开始同步到数据库
    updateSyncProgress(userId, {
      total: starredRepos.length,
      current: 0,
      percentage: 50,
      message: '正在同步到数据库...',
    });

    // 获取用户已有的 stars
    const existingStars = await prisma.userStar.findMany({
      where: { userId },
      include: { repository: true },
    });

    const existingRepoIds = new Set(
      existingStars.map(star => star.repository.githubId)
    );

    let processed = 0;
    const batchSize = 10;

    // 批量处理仓库数据
    for (let i = 0; i < starredRepos.length; i += batchSize) {
      const batch = starredRepos.slice(i, i + batchSize);
      
      await Promise.all(
        batch.map(async (starredRepo) => {
          try {
            // 检查是否已存在
            if (existingRepoIds.has(starredRepo.id)) {
              return;
            }

            // 创建或更新仓库记录
            const repository = await findOrCreateRepository({
              githubId: starredRepo.id,
              name: starredRepo.name,
              fullName: starredRepo.full_name,
              description: starredRepo.description,
              language: starredRepo.language,
              starsCount: starredRepo.stargazers_count,
              htmlUrl: starredRepo.html_url,
              updatedAt: new Date(starredRepo.updated_at),
            });

            // 创建用户 star 记录
            await createUserStar({
              userId,
              repositoryId: repository.id,
              starredAt: new Date(starredRepo.starred_at),
            });

          } catch (error) {
            console.error(`Error processing repository ${starredRepo.full_name}:`, error);
            // 继续处理其他仓库，不中断整个同步过程
          }
        })
      );

      processed += batch.length;
      
      // 更新进度
      updateSyncProgress(userId, {
        current: processed,
        percentage: 50 + Math.round((processed / starredRepos.length) * 50),
        message: `正在同步到数据库... (${processed}/${starredRepos.length})`,
      });

      // 添加小延迟避免数据库压力过大
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 同步完成
    updateSyncProgress(userId, {
      status: 'completed',
      current: starredRepos.length,
      total: starredRepos.length,
      percentage: 100,
      message: `同步完成！共同步 ${starredRepos.length} 个 starred repositories`,
      completedAt: new Date(),
    });

  } catch (error) {
    const githubError = parseGitHubError(error);
    updateSyncProgress(userId, {
      status: 'error',
      message: getUserFriendlyMessage(githubError),
      error: githubError.message,
      completedAt: new Date(),
    });
    throw error;
  }
}

// 更新同步进度
function updateSyncProgress(userId: string, updates: Partial<SyncProgress>) {
  const current = syncStates.get(userId);
  if (current) {
    syncStates.set(userId, { ...current, ...updates });
  }
}

// 获取同步状态
export function getSyncProgress(userId: string): SyncProgress | null {
  return syncStates.get(userId) || null;
}

// 清理同步状态
export function clearSyncProgress(userId: string) {
  syncStates.delete(userId);
}
