import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';

export const runtime = 'nodejs';
import { getSyncProgress, clearSyncProgress } from '../stars/route';

// 取消同步任务
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const currentProgress = getSyncProgress(userId);

    if (!currentProgress) {
      return NextResponse.json(
        { error: 'No sync task found' },
        { status: 404 }
      );
    }

    if (currentProgress.status !== 'running') {
      return NextResponse.json(
        { error: 'Sync task is not running' },
        { status: 400 }
      );
    }

    // 更新状态为已取消
    const cancelledProgress = {
      ...currentProgress,
      status: 'cancelled' as const,
      message: '同步已被用户取消',
      completedAt: new Date(),
    };

    // 这里应该设置取消标志，让正在运行的同步任务检查并停止
    // 由于我们使用内存存储，直接更新状态
    clearSyncProgress(userId);

    return NextResponse.json({
      message: 'Sync cancelled successfully',
      progress: cancelledProgress,
    });

  } catch (error) {
    console.error('Cancel sync error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
