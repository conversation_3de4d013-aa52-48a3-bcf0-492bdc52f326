import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';

export const runtime = 'nodejs';
import { getSyncProgress } from '../stars/route';

// 获取同步状态
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const progress = getSyncProgress(userId);

    if (!progress) {
      return NextResponse.json({
        status: 'idle',
        current: 0,
        total: 0,
        percentage: 0,
        message: '没有正在进行的同步任务',
      });
    }

    return NextResponse.json(progress);

  } catch (error) {
    console.error('Get sync status error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
