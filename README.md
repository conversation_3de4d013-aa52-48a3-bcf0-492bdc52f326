# GitHub Star Manager - 功能与技术规格

## 项目概述
一个基于 Next.js 15 的 GitHub Star 管理工具，帮助用户整理和回顾历史收藏的开源项目。

## 核心功能规格

### 1. 用户认证与授权
- **GitHub OAuth 集成**
  - 使用 GitHub OAuth App 进行用户认证
  - 获取用户的 starred repositories 读取权限
  - 安全存储用户访问令牌
  - 支持用户登出和重新授权

### 2. 数据获取与存储
- **GitHub API 集成**
  - 获取用户所有 starred repositories
  - 支持分页加载大量 star 项目
  - 获取项目基本信息：名称、描述、语言、star 数、更新时间、URL
  - 获取项目 README 内容
  - 实现增量同步，只获取新增的 star

### 3. 内容处理与分析
- **README 提炼与翻译**
  - AI 驱动的 README 内容总结（提取核心功能和用途）
  - 自动翻译为中文（保持技术术语准确性）
  - 生成项目简介摘要（50-100 字）
  - 缓存处理结果避免重复请求

### 4. 分类与标签系统
- **自动标签生成**
  - 基于项目语言自动生成标签
  - 基于项目描述和 README 内容智能生成分类标签
  - 支持用户手动添加、编辑、删除标签
  - 预设常用标签：前端、后端、工具、库、框架、AI/ML 等

### 5. 排序与筛选功能
- **多维度排序**
  - 按 star 数量排序（升序/降序）
  - 按更新时间排序（最新/最旧）
  - 按添加到 star 的时间排序
  - 按项目名称字母排序
- **高级筛选**
  - 按编程语言筛选
  - 按标签筛选（支持多标签组合）
  - 按 star 数量范围筛选
  - 按最后更新时间范围筛选
  - 全文搜索（项目名称、描述、标签）

### 6. 用户界面功能
- **项目展示**
  - 卡片式布局展示项目
  - 列表式紧凑展示模式
  - 项目详情弹窗/页面
  - 响应式设计支持移动端
- **用户交互**
  - 收藏/取消收藏项目
  - 项目笔记功能
  - 导出功能（JSON/CSV）
  - 批量操作（批量添加标签等）

## 技术架构规格

### 1. 前端技术栈
- **框架**: Next.js 15 (App Router)
- **样式**: Tailwind CSS 3.x
- **UI 组件**: 
  - Headless UI 或 Radix UI（无样式组件库）
  - Lucide React（图标库）
- **状态管理**: 
  - React Server Components（服务端状态）
  - useState/useReducer（客户端状态）
  - React Query/SWR（数据缓存）

### 2. 后端技术栈
- **API Routes**: Next.js 15 API Routes
- **数据库**: 
  - PostgreSQL（推荐）或 SQLite（开发）
  - Prisma ORM 进行数据库操作
- **认证**: NextAuth.js 5.x
- **外部服务**:
  - GitHub REST API v4
  - AI 服务（OpenAI GPT-4 或 Claude）用于内容分析

### 3. 数据模型设计
```sql
-- 用户表
users (id, github_id, username, avatar_url, access_token, created_at, updated_at)

-- 项目表
repositories (id, github_id, name, full_name, description, language, stars_count, updated_at, html_url)

-- 用户收藏关系表
user_stars (id, user_id, repository_id, starred_at, notes)

-- 标签表
tags (id, name, color, created_by_user_id)

-- 项目标签关系表
repository_tags (repository_id, tag_id)

-- AI 处理结果缓存表
ai_summaries (repository_id, summary_zh, summary_en, processed_at)
```

### 4. API 端点设计
```
GET  /api/auth/github          # GitHub OAuth 登录
POST /api/auth/callback        # OAuth 回调处理
GET  /api/user/profile         # 获取用户信息
GET  /api/stars                # 获取用户 star 列表
POST /api/stars/sync           # 同步用户 GitHub stars
GET  /api/repositories/:id     # 获取单个项目详情
POST /api/repositories/:id/summary # 生成/获取项目摘要
GET  /api/tags                 # 获取标签列表
POST /api/tags                 # 创建新标签
PUT  /api/repositories/:id/tags # 更新项目标签
```

### 5. 页面结构
```
/                             # 首页/登录页
/dashboard                    # 主面板（项目列表）
/repository/[id]              # 项目详情页
/settings                     # 用户设置页
/tags                         # 标签管理页
```

### 6. UI/UX 设计要求
- **现代设计风格**
  - 使用深色/浅色主题切换
  - 简洁的卡片式设计
  - 清晰的视觉层次
  - 流畅的动画过渡效果
- **用户体验**
  - 快速加载和响应
  - 直观的导航和操作
  - 清晰的状态反馈
  - 优秀的移动端体验

### 7. 性能优化
- **前端优化**
  - 虚拟滚动处理大量数据
  - 图片懒加载
  - 代码分割和懒加载
- **后端优化**
  - 数据库查询优化
  - Redis 缓存热点数据
  - API 响应缓存

### 8. 部署与环境
- **开发环境**: 本地 Next.js 开发服务器
- **生产部署**: Vercel 或 Netlify
- **数据库**: Railway、Supabase 或 PlanetScale
- **环境变量**: GitHub OAuth 密钥、数据库连接、AI API 密钥

## 开发优先级
1. **Phase 1**: 用户认证 + 基础数据获取
2. **Phase 2**: 项目展示 + 基础筛选排序
3. **Phase 3**: AI 内容处理 + 标签系统
4. **Phase 4**: 高级功能 + UI 优化
5. **Phase 5**: 性能优化 + 部署上线

## 成功指标
- 支持处理 1000+ starred repositories
- 页面加载时间 < 2s
- AI 内容处理准确率 > 85%
- 移动端完全可用
- 用户操作流畅无卡顿