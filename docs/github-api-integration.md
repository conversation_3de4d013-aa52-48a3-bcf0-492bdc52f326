# GitHub API 集成功能文档

## 概述

本项目实现了完整的 GitHub API 集成功能，允许用户同步和管理他们的 GitHub starred repositories。

## 功能特性

### 1. GitHub API 客户端 (`lib/github-api.ts`)

- **认证**: 使用用户的 GitHub access token 进行 API 调用
- **分页处理**: 自动处理 GitHub API 的分页响应
- **Rate Limit 管理**: 自动检查和处理 API 使用限制
- **错误处理**: 完善的错误处理和重试机制
- **数据获取**: 支持获取用户信息、starred repositories、单个仓库详情和 README

### 2. 数据同步 API

#### POST /api/sync/stars
开始同步用户的 starred repositories

**请求**: 无需参数
**响应**:
```json
{
  "message": "Sync started successfully",
  "progress": {
    "status": "running",
    "current": 0,
    "total": 0,
    "percentage": 0,
    "message": "正在初始化同步...",
    "startedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### GET /api/sync/status
获取当前同步状态

**响应**:
```json
{
  "status": "running",
  "current": 150,
  "total": 500,
  "percentage": 30,
  "message": "正在同步到数据库... (150/500)",
  "startedAt": "2024-01-01T00:00:00.000Z"
}
```

#### POST /api/sync/cancel
取消正在进行的同步

**响应**:
```json
{
  "message": "Sync cancelled successfully",
  "progress": {
    "status": "cancelled",
    "message": "同步已被用户取消",
    "completedAt": "2024-01-01T00:05:00.000Z"
  }
}
```

### 3. 数据查询 API

#### GET /api/stars
获取用户已同步的 starred repositories

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `search`: 搜索关键词
- `language`: 编程语言筛选
- `sortBy`: 排序字段 (starredAt, starsCount, updatedAt)
- `sortOrder`: 排序方向 (asc, desc)
- `tagIds`: 标签 ID 列表 (逗号分隔)

**响应**:
```json
{
  "data": [
    {
      "id": "star_id",
      "starredAt": "2024-01-01T00:00:00.000Z",
      "repository": {
        "id": "repo_id",
        "githubId": 123456,
        "name": "awesome-project",
        "fullName": "user/awesome-project",
        "description": "An awesome project",
        "language": "TypeScript",
        "starsCount": 1000,
        "htmlUrl": "https://github.com/user/awesome-project",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 500,
    "totalPages": 25,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## 使用方法

### 1. 基本设置

确保在 `.env.local` 中配置了正确的 GitHub OAuth 应用信息：

```env
GITHUB_ID=your_github_app_id
GITHUB_SECRET=your_github_app_secret
```

### 2. 用户认证

用户需要通过 GitHub OAuth 登录，系统会自动获取必要的权限：
- `read:user`: 读取用户基本信息
- `user:email`: 读取用户邮箱
- `repo`: 访问仓库信息（包括 starred repositories）

### 3. 同步数据

#### 使用 React Hook

```tsx
import { useSyncStatus } from '@/hooks/use-sync-status';

function SyncComponent() {
  const { progress, isLoading, error, startSync, cancelSync } = useSyncStatus();

  return (
    <div>
      <button onClick={startSync} disabled={isLoading}>
        开始同步
      </button>
      {progress && (
        <div>
          <p>状态: {progress.status}</p>
          <p>进度: {progress.percentage}%</p>
          <p>消息: {progress.message}</p>
        </div>
      )}
    </div>
  );
}
```

#### 直接调用 API

```javascript
// 开始同步
const response = await fetch('/api/sync/stars', {
  method: 'POST',
});
const result = await response.json();

// 获取同步状态
const statusResponse = await fetch('/api/sync/status');
const status = await statusResponse.json();
```

### 4. 查询数据

```javascript
// 获取 starred repositories
const response = await fetch('/api/stars?page=1&limit=20&language=TypeScript');
const data = await response.json();
```

## 错误处理

系统实现了完善的错误处理机制：

### 错误类型

- `NETWORK_ERROR`: 网络连接错误
- `RATE_LIMIT_EXCEEDED`: API 使用限制超出
- `UNAUTHORIZED`: 认证失败
- `FORBIDDEN`: 权限不足
- `NOT_FOUND`: 资源不存在
- `SERVER_ERROR`: 服务器错误
- `TIMEOUT`: 请求超时

### 重试机制

- 自动重试可恢复的错误
- 指数退避算法
- 最大重试次数限制
- 智能延迟计算

## 性能优化

### 1. 分页处理
- 自动处理 GitHub API 分页
- 批量处理数据以减少内存使用
- 支持大量数据的增量同步

### 2. Rate Limit 管理
- 实时监控 API 使用情况
- 自动等待 rate limit 重置
- 智能请求调度

### 3. 数据库优化
- 批量插入操作
- 索引优化
- 重复数据检测

## 安全考虑

### 1. Access Token 安全
- Token 存储在服务端数据库
- 不在客户端暴露敏感信息
- 定期检查 token 有效性

### 2. API 调用验证
- 所有 API 调用都需要用户认证
- 用户数据隔离
- 输入参数验证

## 测试

### 1. 测试页面
访问 `/test-sync` 页面进行功能测试

### 2. API 测试
使用 Postman 或类似工具测试 API 端点

### 3. 单元测试
```bash
npm test
```

## 故障排除

### 常见问题

1. **同步失败**: 检查 GitHub token 是否有效
2. **Rate Limit 错误**: 等待 API 限制重置
3. **网络错误**: 检查网络连接
4. **权限错误**: 确认 OAuth 应用权限配置

### 日志查看
检查服务器日志获取详细错误信息：
```bash
npm run dev
```

## 扩展功能

### 1. 增量同步
系统支持增量同步，只获取新的 starred repositories

### 2. 标签管理
可以为 repositories 添加自定义标签

### 3. AI 摘要
支持为 repositories 生成 AI 摘要

## 技术栈

- **前端**: Next.js 15, React, TypeScript, Tailwind CSS
- **后端**: Next.js API Routes, Prisma ORM
- **数据库**: PostgreSQL
- **认证**: NextAuth.js
- **API**: GitHub REST API v4
