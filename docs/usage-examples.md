# GitHub API 集成使用示例

## 1. 基本同步流程

### 完整的同步示例

```tsx
'use client';

import { useSyncStatus } from '@/hooks/use-sync-status';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';

export function GitHubSyncExample() {
  const { 
    progress, 
    isLoading, 
    error, 
    startSync, 
    cancelSync, 
    clearError 
  } = useSyncStatus();

  const handleSync = async () => {
    try {
      await startSync();
      console.log('同步已开始');
    } catch (err) {
      console.error('启动同步失败:', err);
    }
  };

  const handleCancel = async () => {
    try {
      await cancelSync();
      console.log('同步已取消');
    } catch (err) {
      console.error('取消同步失败:', err);
    }
  };

  return (
    <div className="space-y-4">
      {/* 错误显示 */}
      {error && (
        <Alert variant="destructive">
          <AlertDescription>
            {error}
            <Button variant="ghost" onClick={clearError}>
              关闭
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* 同步控制 */}
      <div className="flex gap-2">
        <Button 
          onClick={handleSync} 
          disabled={isLoading || progress?.status === 'running'}
        >
          {isLoading ? '启动中...' : '开始同步'}
        </Button>
        
        {progress?.status === 'running' && (
          <Button variant="destructive" onClick={handleCancel}>
            取消同步
          </Button>
        )}
      </div>

      {/* 进度显示 */}
      {progress && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>状态: {progress.status}</span>
            <span>{progress.percentage.toFixed(1)}%</span>
          </div>
          
          <Progress value={progress.percentage} />
          
          <div className="text-sm text-muted-foreground">
            {progress.message}
          </div>
          
          {progress.total > 0 && (
            <div className="text-xs text-muted-foreground">
              进度: {progress.current} / {progress.total}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
```

## 2. 直接使用 GitHub API 客户端

### 基本用法

```typescript
import { createGitHubApiClient } from '@/lib/github-api';

async function useGitHubApi(accessToken: string) {
  const client = createGitHubApiClient(accessToken);

  try {
    // 获取当前用户信息
    const user = await client.getCurrentUser();
    console.log('用户信息:', user);

    // 检查 API 使用限制
    const rateLimit = await client.checkRateLimit();
    console.log('API 剩余次数:', rateLimit.remaining);

    // 获取 starred repositories (分页)
    const starredRepos = await client.getStarredRepos({
      page: 1,
      per_page: 50,
      sort: 'created',
      direction: 'desc'
    });
    console.log('Starred repos:', starredRepos.data);

    // 获取所有 starred repositories (自动分页)
    const allStarredRepos = await client.getAllStarredRepos(
      (current, total) => {
        console.log(`进度: ${current}/${total || '?'}`);
      }
    );
    console.log('所有 starred repos:', allStarredRepos.length);

    // 获取单个仓库信息
    const repo = await client.getRepository('facebook', 'react');
    console.log('React 仓库信息:', repo);

    // 获取仓库 README
    const readme = await client.getRepositoryReadme('facebook', 'react');
    if (readme) {
      const content = client.decodeReadmeContent(readme);
      console.log('README 内容:', content.substring(0, 200) + '...');
    }

  } catch (error) {
    console.error('GitHub API 调用失败:', error);
  }
}
```

### 高级用法 - 批量处理

```typescript
import { createGitHubApiClient } from '@/lib/github-api';
import { findOrCreateRepository, createUserStar } from '@/lib/db';

async function batchSyncStars(userId: string, accessToken: string) {
  const client = createGitHubApiClient(accessToken);
  const batchSize = 10;
  
  try {
    // 获取所有 starred repositories
    const starredRepos = await client.getAllStarredRepos(
      (current, total) => {
        console.log(`获取进度: ${current}/${total || '?'}`);
      }
    );

    console.log(`开始同步 ${starredRepos.length} 个仓库`);

    // 批量处理
    for (let i = 0; i < starredRepos.length; i += batchSize) {
      const batch = starredRepos.slice(i, i + batchSize);
      
      await Promise.all(
        batch.map(async (starredRepo) => {
          try {
            // 创建或更新仓库记录
            const repository = await findOrCreateRepository({
              githubId: starredRepo.id,
              name: starredRepo.name,
              fullName: starredRepo.full_name,
              description: starredRepo.description,
              language: starredRepo.language,
              starsCount: starredRepo.stargazers_count,
              htmlUrl: starredRepo.html_url,
              updatedAt: new Date(starredRepo.updated_at),
            });

            // 创建用户 star 记录
            await createUserStar({
              userId,
              repositoryId: repository.id,
              starredAt: new Date(starredRepo.starred_at),
            });

            console.log(`已同步: ${starredRepo.full_name}`);
          } catch (error) {
            console.error(`同步失败 ${starredRepo.full_name}:`, error);
          }
        })
      );

      console.log(`已完成批次 ${Math.floor(i / batchSize) + 1}`);
      
      // 添加延迟避免过快请求
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.log('同步完成');
  } catch (error) {
    console.error('批量同步失败:', error);
  }
}
```

## 3. 查询已同步的数据

### 基本查询

```typescript
// 获取用户的 starred repositories
async function fetchUserStars() {
  try {
    const response = await fetch('/api/stars?page=1&limit=20');
    const data = await response.json();
    
    console.log('Stars:', data.data);
    console.log('分页信息:', data.pagination);
  } catch (error) {
    console.error('获取 stars 失败:', error);
  }
}
```

### 高级查询

```typescript
// 带筛选和排序的查询
async function fetchFilteredStars() {
  const params = new URLSearchParams({
    page: '1',
    limit: '50',
    search: 'react',
    language: 'TypeScript',
    sortBy: 'starsCount',
    sortOrder: 'desc'
  });

  try {
    const response = await fetch(`/api/stars?${params}`);
    const data = await response.json();
    
    console.log('筛选后的 Stars:', data.data);
  } catch (error) {
    console.error('查询失败:', error);
  }
}
```

## 4. 错误处理示例

### 完整的错误处理

```typescript
import { parseGitHubError, getUserFriendlyMessage } from '@/lib/error-handler';

async function robustGitHubApiCall(accessToken: string) {
  const client = createGitHubApiClient(accessToken);

  try {
    const repos = await client.getStarredRepos();
    return repos;
  } catch (error) {
    const githubError = parseGitHubError(error);
    
    switch (githubError.type) {
      case 'RATE_LIMIT_EXCEEDED':
        console.log('API 限制超出，等待重置...');
        await client.waitForRateLimit();
        // 重试
        return await client.getStarredRepos();
        
      case 'UNAUTHORIZED':
        console.error('认证失败，需要重新登录');
        // 重定向到登录页面
        window.location.href = '/';
        break;
        
      case 'NETWORK_ERROR':
        console.error('网络错误，请检查连接');
        break;
        
      default:
        console.error('未知错误:', getUserFriendlyMessage(githubError));
    }
    
    throw githubError;
  }
}
```

## 5. 实时进度监控

### 使用 Server-Sent Events (可选扩展)

```typescript
// 客户端
function useRealtimeSync() {
  const [progress, setProgress] = useState(null);

  useEffect(() => {
    const eventSource = new EventSource('/api/sync/stream');
    
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setProgress(data);
    };

    eventSource.onerror = () => {
      console.error('SSE 连接错误');
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, []);

  return progress;
}
```

## 6. 自定义同步配置

### 配置同步参数

```typescript
import { SyncConfig } from '@/types/github';

const customSyncConfig: SyncConfig = {
  batchSize: 20,           // 批处理大小
  delayBetweenRequests: 200, // 请求间延迟 (ms)
  maxRetries: 5,           // 最大重试次数
  retryDelay: 2000,        // 重试延迟 (ms)
};

// 使用自定义配置进行同步
async function customSync(userId: string, accessToken: string) {
  // 实现自定义同步逻辑
  console.log('使用自定义配置同步:', customSyncConfig);
}
```

## 7. 测试示例

### 单元测试

```typescript
import { createGitHubApiClient } from '@/lib/github-api';
import { parseGitHubError } from '@/lib/error-handler';

describe('GitHub API Client', () => {
  const mockToken = 'test-token';
  
  test('应该正确创建客户端', () => {
    const client = createGitHubApiClient(mockToken);
    expect(client).toBeDefined();
  });

  test('应该正确处理 API 错误', () => {
    const error = new Error('API Error');
    (error as any).response = { status: 401 };
    
    const githubError = parseGitHubError(error);
    expect(githubError.type).toBe('UNAUTHORIZED');
  });
});
```

### 集成测试

```typescript
// 测试完整的同步流程
async function testSyncFlow() {
  const testUserId = 'test-user-id';
  const testToken = process.env.TEST_GITHUB_TOKEN;
  
  if (!testToken) {
    console.log('跳过集成测试：未提供测试 token');
    return;
  }

  try {
    // 测试 API 客户端
    const client = createGitHubApiClient(testToken);
    const user = await client.getCurrentUser();
    console.log('测试用户:', user.login);

    // 测试同步 API
    const response = await fetch('/api/sync/stars', {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${testToken}` }
    });
    
    const result = await response.json();
    console.log('同步结果:', result);
    
  } catch (error) {
    console.error('集成测试失败:', error);
  }
}
```

这些示例展示了如何在实际项目中使用 GitHub API 集成功能。你可以根据具体需求调整和扩展这些示例。
