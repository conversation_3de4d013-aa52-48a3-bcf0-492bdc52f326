// This is your Prisma schema file
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// 用户表
model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]

  // GitHub 相关字段
  githubId      Int?      @unique
  username      String?
  avatarUrl     String?
  accessToken   String?

  // 关联字段
  stars        UserStar[]
  createdTags  Tag[]

  // 时间戳
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  @@index([githubId])
  @@index([email])
}

// 项目表
model Repository {
  id          String   @id @default(cuid())
  githubId    Int      @unique
  name        String
  fullName    String
  description String?
  language    String?
  starsCount  Int      @default(0)
  htmlUrl     String
  
  // 关联字段
  stars       UserStar[]
  tags        RepositoryTag[]
  aiSummary   AiSummary?

  // 时间戳
  updatedAt   DateTime
  createdAt   DateTime @default(now())

  @@index([githubId])
  @@index([language])
  @@index([starsCount])
  @@index([updatedAt])
}

// 用户收藏关系表
model UserStar {
  id           String     @id @default(cuid())
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId       String
  repository   Repository @relation(fields: [repositoryId], references: [id], onDelete: Cascade)
  repositoryId String
  notes        String?
  starredAt    DateTime

  @@unique([userId, repositoryId])
  @@index([starredAt])
}

// 标签表
model Tag {
  id            String         @id @default(cuid())
  name          String
  color         String
  createdBy     User          @relation(fields: [createdByUserId], references: [id])
  createdByUserId String
  repositories  RepositoryTag[]

  @@unique([name, createdByUserId])
}

// 项目标签关系表
model RepositoryTag {
  repository   Repository @relation(fields: [repositoryId], references: [id], onDelete: Cascade)
  repositoryId String
  tag         Tag       @relation(fields: [tagId], references: [id], onDelete: Cascade)
  tagId       String

  @@id([repositoryId, tagId])
}

// AI 摘要缓存表
model AiSummary {
  id           String     @id @default(cuid())
  repository   Repository @relation(fields: [repositoryId], references: [id], onDelete: Cascade)
  repositoryId String     @unique
  summaryZh    String     @db.Text
  summaryEn    String     @db.Text
  processedAt  DateTime   @default(now())

  @@index([processedAt])
}
