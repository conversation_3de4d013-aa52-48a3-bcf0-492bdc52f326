import type { NextAuthConfig } from 'next-auth';
import type { JWT } from '@auth/core/jwt';
import Github from 'next-auth/providers/github';
import { PrismaAdapter } from '@auth/prisma-adapter';
import { prisma } from '@/lib/prisma';

// 扩展 JWT 类型
interface ExtendedJWT extends JWT {
  accessToken?: string;
  githubId?: number;
}

// GitHub Profile 类型
interface GitHubProfile {
  id: number;
  login: string;
  avatar_url: string;
  email: string | null;
  name: string | null;
}

export const authConfig = {
  debug: process.env.NODE_ENV === 'development',
  adapter: PrismaAdapter(prisma),
  providers: [
    Github({
      clientId: process.env.GITHUB_ID,
      clientSecret: process.env.GITHUB_SECRET,
      authorization: {
        params: {
          scope: 'read:user user:email repo'
        }
      }
    })
  ],
  callbacks: {
    async session({ session, user }) {
      // 获取用户的完整信息
      const dbUser = await prisma.user.findUnique({
        where: { id: user.id },
        select: {
          id: true,
          githubId: true,
          username: true,
          accessToken: true
        }
      });

      return {
        ...session,
        user: {
          ...session.user,
          ...dbUser
        },
      };
    },
    async signIn({ profile, account }) {
      if (!profile?.email) {
        console.error('No email provided by GitHub');
        return false;
      }

      const githubProfile = profile as unknown as GitHubProfile;
      
      try {
        // 使用 email 查找用户，如果不存在则创建新用户
        const user = await prisma.user.upsert({
          where: { email: profile.email },
          update: {
            name: profile.name ?? '',
            image: githubProfile.avatar_url,
            githubId: githubProfile.id,
            username: githubProfile.login,
            avatarUrl: githubProfile.avatar_url,
            accessToken: account?.access_token,
          },
          create: {
            email: profile.email,
            name: profile.name ?? '',
            image: githubProfile.avatar_url,
            githubId: githubProfile.id,
            username: githubProfile.login,
            avatarUrl: githubProfile.avatar_url,
            accessToken: account?.access_token,
          },
        });

        // 创建或更新 Account 记录
        await prisma.account.upsert({
          where: {
            provider_providerAccountId: {
              provider: 'github',
              providerAccountId: githubProfile.id.toString(),
            },
          },
          update: {
            access_token: account?.access_token,
            scope: account?.scope,
            token_type: account?.token_type,
            userId: user.id,
          },
          create: {
            userId: user.id,
            type: account?.type ?? 'oauth',
            provider: 'github',
            providerAccountId: githubProfile.id.toString(),
            access_token: account?.access_token,
            scope: account?.scope,
            token_type: account?.token_type,
          },
        });

        return true;
      } catch (error) {
        console.error('Error in sign in:', error);
        return false;
      }
    },
    async jwt({ token, account, profile }) {
      if (account && profile) {
        const githubProfile = profile as unknown as GitHubProfile;
        token.accessToken = account.access_token;
        token.githubId = githubProfile.id;
      }
      return token as ExtendedJWT;
    }
  },
  pages: {
    signIn: '/',
    signOut: '/',
    error: '/',
  },
} satisfies NextAuthConfig;
