import { prisma } from '../lib/prisma'
import {
  findOrCreateUser,
  findOrCreateRepository,
  createUserStar,
  createTag,
  addTagToRepository,
  upsertAiSummary,
} from '../lib/db'

async function cleanupDatabase() {
  // 清理所有测试数据
  await prisma.aiSummary.deleteMany()
  await prisma.repositoryTag.deleteMany()
  await prisma.tag.deleteMany()
  await prisma.userStar.deleteMany()
  await prisma.repository.deleteMany()
  await prisma.user.deleteMany()
}

async function testDatabase() {
  console.log('开始数据库测试...')

  try {
    // 清理数据库
    await cleanupDatabase()
    console.log('数据库清理完成')

    // 1. 测试用户创建
    const user = await findOrCreateUser({
      githubId: 12345,
      username: 'testuser',
      avatarUrl: 'https://github.com/testuser.png',
      email: '<EMAIL>',
      accessToken: 'test-token',
    })
    console.log('用户创建成功:', user.username)

    // 2. 测试仓库创建
    const repo = await findOrCreateRepository({
      githubId: 67890,
      name: 'test-repo',
      fullName: 'testuser/test-repo',
      description: 'A test repository',
      language: 'TypeScript',
      starsCount: 100,
      htmlUrl: 'https://github.com/testuser/test-repo',
      updatedAt: new Date(),
    })
    console.log('仓库创建成功:', repo.name)

    // 3. 测试 Star 功能
    await createUserStar({
      userId: user.id,
      repositoryId: repo.id,
      starredAt: new Date(),
    })
    console.log('Star 创建成功')

    // 4. 测试标签功能
    const tag = await createTag({
      name: 'test-tag',
      color: '#ff0000',
      createdByUserId: user.id,
    })
    console.log('标签创建成功:', tag.name)

    await addTagToRepository(repo.id, tag.id)
    console.log('标签添加到仓库成功')

    // 5. 测试 AI 摘要
    await upsertAiSummary({
      repositoryId: repo.id,
      summaryZh: '这是一个测试仓库的中文摘要',
      summaryEn: 'This is an English summary of the test repository',
    })
    console.log('AI 摘要创建成功')

    // 6. 测试关联查询
    const repoWithRelations = await prisma.repository.findFirst({
      where: { id: repo.id },
      include: {
        stars: {
          include: {
            user: true,
          },
        },
        tags: {
          include: {
            tag: true,
          },
        },
        aiSummary: true,
      },
    })

    console.log('\n数据验证:')
    console.log('- 仓库名称:', repoWithRelations?.name)
    console.log('- Star 用户:', repoWithRelations?.stars[0].user.username)
    console.log('- 标签:', repoWithRelations?.tags[0].tag.name)
    console.log('- AI 摘要 (中文):', repoWithRelations?.aiSummary?.summaryZh)

    console.log('\n所有测试通过！')

  } catch (error) {
    console.error('测试失败:', error)
    throw error
  } finally {
    // 清理数据
    await cleanupDatabase()
    // 断开数据库连接
    await prisma.$disconnect()
  }
}

// 运行测试
testDatabase().catch((error) => {
  console.error('测试脚本执行失败:', error)
  process.exit(1)
})